package com.xiang.proxy.server.outbound.impl;

import com.xiang.proxy.server.core.ProxyRequest;
import com.xiang.proxy.server.metrics.AdvancedMetrics;
import com.xiang.proxy.server.metrics.PerformanceMetrics;
import com.xiang.proxy.server.outbound.AsyncOutboundConnection;
import com.xiang.proxy.server.outbound.OutboundConnection;
import com.xiang.proxy.server.outbound.OutboundHandler;
import com.xiang.proxy.server.outbound.OutboundConfig;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.channel.*;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 异步TCP直连出站处理器
 * 立即返回连接对象，支持连接建立过程中的数据缓存
 */
public class AsyncTcpDirectOutboundHandler implements OutboundHandler {
    private static final Logger logger = LoggerFactory.getLogger(AsyncTcpDirectOutboundHandler.class);
    
    private final String outboundId;
    private final OutboundConfig config;
    private final Bootstrap bootstrap;
    private final AtomicInteger connectionCounter = new AtomicInteger(0);
    private final AtomicInteger successCounter = new AtomicInteger(0);
    private final AtomicInteger failureCounter = new AtomicInteger(0);

    // 活跃连接管理 - 替代连接池
    private final ConcurrentHashMap<String, AsyncOutboundConnection> activeConnections = new ConcurrentHashMap<>();

    // 性能监控
    private final AdvancedMetrics advancedMetrics = AdvancedMetrics.getInstance();
    private final PerformanceMetrics performanceMetrics = PerformanceMetrics.getInstance();
    
    public AsyncTcpDirectOutboundHandler(String outboundId, OutboundConfig config) {
        this.outboundId = outboundId;
        this.config = config;
        this.bootstrap = createBootstrap();
        
        logger.info("异步TCP直连出站处理器初始化完成: {}", outboundId);
    }
    
    /**
     * 创建Bootstrap模板
     */
    private Bootstrap createBootstrap() {
        Bootstrap bootstrap = new Bootstrap();
        bootstrap.channel(NioSocketChannel.class)
                .option(ChannelOption.TCP_NODELAY, true)
                .option(ChannelOption.SO_KEEPALIVE, true)
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, config.getConnectTimeout())
                .option(ChannelOption.SO_RCVBUF, 64 * 1024)
                .option(ChannelOption.SO_SNDBUF, 64 * 1024)
                .handler(new ChannelInitializer<SocketChannel>() {
                    @Override
                    protected void initChannel(SocketChannel ch) {
                        // 基础pipeline，后续会根据需要添加处理器
                    }
                });
        
        return bootstrap;
    }
    
    @Override
    public CompletableFuture<OutboundConnection> connect(ProxyRequest request) {
        long startTime = System.currentTimeMillis();
        connectionCounter.incrementAndGet();
        
        // 记录请求和协议统计
        advancedMetrics.recordRequest();
        advancedMetrics.recordProtocolUsage("TCP");
        performanceMetrics.incrementTcpConnections();
        performanceMetrics.incrementTotalConnections();
        
        logger.debug("开始建立异步TCP连接: {} -> {}:{}",
                request.getRequestId(), request.getTargetHost(), request.getTargetPort());
        
        // 立即创建AsyncOutboundConnection对象
        AsyncOutboundConnection asyncConnection = AsyncOutboundConnection.builder()
                .target(request.getTargetHost(), request.getTargetPort())
                .protocol(request.getProtocol())
                .createTime(startTime)
                .attribute(AsyncOutboundConnection.Attributes.OUTBOUND_ID, outboundId)
                .attribute(AsyncOutboundConnection.Attributes.CLIENT_CONNECTION_ID, request.getClientId())
                .attribute(AsyncOutboundConnection.Attributes.SESSION_ID, request.getSessionId())
                .build();
        
        // 标记连接正在建立
        asyncConnection.markConnecting();

        // 将连接添加到活跃连接管理
        activeConnections.put(asyncConnection.getConnectionId(), asyncConnection);

        // 在后台异步建立连接
        establishConnectionAsync(asyncConnection, request, startTime);
        
        // 立即返回连接对象（创建OutboundConnection）
        OutboundConnection wrappedConnection = OutboundConnection.builder()
                .connectionId(asyncConnection.getConnectionId())
                .backendChannel(asyncConnection.getBackendChannel()) // 可能为null
                .target(asyncConnection.getTargetHost(), asyncConnection.getTargetPort())
                .protocol(asyncConnection.getProtocol())
                .createTime(startTime)
                .attribute(AsyncOutboundConnection.Attributes.OUTBOUND_ID, outboundId)
                .attribute(AsyncOutboundConnection.Attributes.CLIENT_CONNECTION_ID, request.getClientId())
                .attribute(AsyncOutboundConnection.Attributes.SESSION_ID, request.getSessionId())
                .attribute(AsyncOutboundConnection.Attributes.ASYNC_CONNECTION, asyncConnection) // 存储异步连接引用
                .build();

        return CompletableFuture.completedFuture(wrappedConnection);
    }
    
    /**
     * 异步建立连接
     */
    private void establishConnectionAsync(AsyncOutboundConnection asyncConnection, ProxyRequest request, long startTime) {
        EventLoopGroup eventLoopGroup = request.getClientChannel().eventLoop();
        Bootstrap connectionBootstrap = bootstrap.clone().group(eventLoopGroup);
        
        ChannelFuture connectFuture = connectionBootstrap.connect(request.getTargetHost(), request.getTargetPort());
        
        connectFuture.addListener((ChannelFutureListener) future -> {
            long connectTime = System.currentTimeMillis() - startTime;
            
            if (future.isSuccess()) {
                Channel backendChannel = future.channel();
                logger.debug("异步TCP连接建立成功: {} -> {}:{}, 耗时: {}ms",
                        request.getRequestId(), request.getTargetHost(), request.getTargetPort(), connectTime);

                // 设置后端连接
                asyncConnection.setBackendChannel(backendChannel);
                asyncConnection.setAttribute(AsyncOutboundConnection.Attributes.CONNECT_TIME, connectTime);

                // 添加连接关闭监听器，自动清理
                backendChannel.closeFuture().addListener(closeFuture -> {
                    activeConnections.remove(asyncConnection.getConnectionId());
                    logger.debug("连接已关闭并清理: {}", asyncConnection.getConnectionId());
                });

                // 记录成功指标
                successCounter.incrementAndGet();
                advancedMetrics.recordLatency("tcp_connect", connectTime);
                advancedMetrics.recordConnectionQuality(request.getTargetHost(), true, connectTime);
                advancedMetrics.recordResponse();

            } else {
                logger.warn("异步TCP连接建立失败: {} -> {}:{}, 耗时: {}ms",
                        request.getRequestId(), request.getTargetHost(), request.getTargetPort(),
                        connectTime, future.cause());

                // 设置连接失败
                asyncConnection.setConnectionFailed(future.cause());

                // 从活跃连接中移除失败的连接
                activeConnections.remove(asyncConnection.getConnectionId());

                // 记录失败指标
                failureCounter.incrementAndGet();
                advancedMetrics.recordConnectionQuality(request.getTargetHost(), false, connectTime);
                advancedMetrics.recordError("tcp_connect_failed");
            }
        });
    }
    
    @Override
    public CompletableFuture<Void> sendData(OutboundConnection connection, ByteBuf data) {
        // 检查是否是异步连接
        AsyncOutboundConnection asyncConnection = connection.getAttribute(AsyncOutboundConnection.Attributes.ASYNC_CONNECTION);
        if (asyncConnection != null) {
            return asyncConnection.sendData(data);
        }

        // 兼容原有的OutboundConnection
        Channel channel = connection.getBackendChannel();
        if (channel == null || !channel.isActive()) {
            return CompletableFuture.failedFuture(new IllegalStateException("连接不可用"));
        }

        CompletableFuture<Void> future = new CompletableFuture<>();
        channel.writeAndFlush(data).addListener(f -> {
            if (f.isSuccess()) {
                future.complete(null);
            } else {
                future.completeExceptionally(f.cause());
            }
        });

        return future;
    }
    
    @Override
    public CompletableFuture<Void> closeConnection(OutboundConnection connection) {
        // 检查是否是异步连接
        AsyncOutboundConnection asyncConnection = connection.getAttribute(AsyncOutboundConnection.Attributes.ASYNC_CONNECTION);
        if (asyncConnection != null) {
            return asyncConnection.close();
        }

        // 兼容原有的OutboundConnection
        Channel channel = connection.getBackendChannel();
        if (channel != null && channel.isActive()) {
            CompletableFuture<Void> future = new CompletableFuture<>();
            channel.close().addListener(f -> {
                if (f.isSuccess()) {
                    future.complete(null);
                } else {
                    future.completeExceptionally(f.cause());
                }
            });
            return future;
        }

        return CompletableFuture.completedFuture(null);
    }
    
    @Override
    public String getOutboundId() {
        return outboundId;
    }
    
    @Override
    public OutboundConfig getConfig() {
        return config;
    }
    
    @Override
    public boolean isAvailable() {
        return true;
    }

    @Override
    public String getType() {
        return "AsyncTcpDirect";
    }

    @Override
    public int getPriority() {
        return 10; // 高优先级
    }
    
    /**
     * 获取连接统计信息
     */
    public String getStats() {
        return String.format("AsyncTcpDirectOutbound{id='%s', total=%d, success=%d, failure=%d, active=%d, successRate=%.2f%%}",
                outboundId, connectionCounter.get(), successCounter.get(), failureCounter.get(),
                activeConnections.size(),
                connectionCounter.get() > 0 ? (double) successCounter.get() / connectionCounter.get() * 100.0 : 0.0);
    }

    /**
     * 获取活跃连接数
     */
    public int getActiveConnectionCount() {
        return activeConnections.size();
    }

    /**
     * 清理不活跃的连接
     */
    public void cleanupInactiveConnections() {
        long currentTime = System.currentTimeMillis();
        int cleanedCount = 0;

        activeConnections.entrySet().removeIf(entry -> {
            AsyncOutboundConnection connection = entry.getValue();
            // 清理超过5分钟未活跃的连接
            if (currentTime - connection.getLastActiveTime() > 300000) {
                connection.close();
                return true;
            }
            return false;
        });

        if (cleanedCount > 0) {
            logger.info("清理了 {} 个不活跃连接", cleanedCount);
        }
    }

    /**
     * 关闭所有活跃连接
     */
    public void closeAllConnections() {
        logger.info("关闭所有活跃连接，数量: {}", activeConnections.size());

        activeConnections.values().forEach(connection -> {
            try {
                connection.close();
            } catch (Exception e) {
                logger.warn("关闭连接时发生异常: {}", connection.getConnectionId(), e);
            }
        });

        activeConnections.clear();
    }

    @Override
    public void destroy() {
        closeAllConnections();
        logger.info("AsyncTcpDirectOutboundHandler已销毁: {}", outboundId);
    }
}
