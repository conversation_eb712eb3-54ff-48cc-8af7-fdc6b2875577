C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\outbound\impl\TcpDirectOutboundHandler.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\filter\FilterResult.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\inbound\InboundServerStatistics.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\util\GeoIPUtil.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\inbound\InboundHandler.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\protocol\MultiplexProtocol.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\pool\EnhancedConnectionPool.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\ProxyServer.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\core\ProxyProcessor.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\inbound\impl\multiplex\MultiplexInboundHandler.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\pool\OptimizedConnectionPool.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\core\ProxyProcessorFactory.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\config\ProxyServerConfigManager.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\util\MemoryOptimizer.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\config\ConnectionPoolConfig.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\inbound\InboundServerConfig.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\util\ConnectionPoolDiagnostics.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\inbound\AbstractInboundServer.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\config\ProxyServerV2ConfigManager.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\pool\ConnectionPool.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\handler\MultiplexProxyHandler.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\outbound\OutboundHandler.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\outbound\AsyncConnectionExample.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\exception\ResourceCleanupManager.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\config\ProxyProcessorConfig.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\outbound\OutboundHandlerType.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\metrics\AdvancedMetrics.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\config\properties\ProxyServerProperties.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\protocol\MultiplexProtocolDetector.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\router\RouteRule.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\core\AdaptiveQueueManager.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\filter\FilterStats.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\router\RouteMatcher.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\outbound\impl\UdpDirectOutboundHandler.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\router\ValidationResult.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\filter\MaliciousContentLoader.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\util\ThreadPoolPerformanceAnalyzer.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\inbound\InboundHandlerStatistics.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\outbound\OutboundConfig.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\router\Router.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\config\binder\ConfigurationBinder.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\router\DefaultRouter.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\cache\CacheManager.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\exception\ExceptionHandlingConfig.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\config\properties\ProxyServerV2Properties.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\util\ConnectionKeyUtils.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\inbound\impl\multiplex\MultiplexBackendDataHandler.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\exception\ExceptionHandler.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\filter\BlockReason.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\outbound\AsyncOutboundConnection.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\core\ProxyRequest.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\config\annotation\ConfigurationProperties.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\core\ProxyResponse.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\outbound\OutboundConnection.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\router\RouteResult.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\core\ProxyServerInitializer.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\inbound\InboundServer.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\core\BatchProxyProcessor.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\filter\GeoLocationFilter.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\inbound\impl\multiplex\MultiplexSession.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\router\RouteStatistics.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\outbound\OutboundStatistics.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\auth\AuthManager.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\blacklist\HostBlacklist.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\outbound\impl\AsyncTcpDirectOutboundHandler.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\inbound\InboundServerManager.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\auth\AuthConfig.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\metrics\PerformanceMetrics.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\handler\MultiplexBackendHandler.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\core\ProxyMetrics.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\inbound\impl\multiplex\MultiplexInboundServer.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\ProxyServerV2.java
C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\src\main\java\com\xiang\proxy\server\ssl\SslContextManager.java
