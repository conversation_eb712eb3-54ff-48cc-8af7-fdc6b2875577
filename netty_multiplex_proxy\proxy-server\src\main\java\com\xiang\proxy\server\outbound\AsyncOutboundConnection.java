package com.xiang.proxy.server.outbound;

import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.util.ReferenceCountUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.Queue;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 异步出站连接实现
 * 支持连接建立过程中的数据缓存，避免竞态条件
 */
public class AsyncOutboundConnection {
    private static final Logger logger = LoggerFactory.getLogger(AsyncOutboundConnection.class);
    
    // 缓存队列的最大大小，防止内存泄漏
    private static final int MAX_QUEUE_SIZE = 1000;
    
    private final String connectionId;
    private final String targetHost;
    private final int targetPort;
    private final String protocol;
    private final long createTime;
    private final AtomicLong bytesTransferred;
    private final Map<String, Object> attributes;
    
    // 连接状态管理
    private volatile Channel backendChannel;
    private final AtomicBoolean connected = new AtomicBoolean(false);
    private final AtomicBoolean connecting = new AtomicBoolean(false);
    private volatile boolean active = true;
    private volatile long lastActiveTime;
    
    // 数据缓存队列 - 在连接建立前缓存数据
    private final Queue<ByteBuf> pendingDataQueue = new ConcurrentLinkedQueue<>();
    private final AtomicLong queuedBytes = new AtomicLong(0);
    
    // 连接建立的Future
    private final CompletableFuture<Channel> connectionFuture = new CompletableFuture<>();
    
    private AsyncOutboundConnection(Builder builder) {
        this.connectionId = builder.connectionId != null ? builder.connectionId : UUID.randomUUID().toString();
        this.targetHost = builder.targetHost;
        this.targetPort = builder.targetPort;
        this.protocol = builder.protocol;
        this.createTime = builder.createTime > 0 ? builder.createTime : System.currentTimeMillis();
        this.bytesTransferred = new AtomicLong(0);
        this.attributes = new HashMap<>(builder.attributes);
        this.lastActiveTime = this.createTime;
        
        // 如果builder中已有channel，直接设置为已连接
        if (builder.backendChannel != null) {
            setBackendChannel(builder.backendChannel);
        }
    }
    
    /**
     * 设置后端连接通道
     */
    public void setBackendChannel(Channel channel) {
        if (channel == null) {
            logger.warn("尝试设置空的后端通道: {}", connectionId);
            return;
        }
        
        this.backendChannel = channel;
        this.connected.set(true);
        this.connecting.set(false);
        this.connectionFuture.complete(channel);
        
        logger.debug("后端连接已建立: {}, 开始处理缓存数据", connectionId);
        
        // 处理缓存的数据
        flushPendingData();
    }
    
    /**
     * 标记连接正在建立中
     */
    public void markConnecting() {
        this.connecting.set(true);
        logger.debug("连接开始建立: {}", connectionId);
    }
    
    /**
     * 连接建立失败
     */
    public void setConnectionFailed(Throwable cause) {
        this.connecting.set(false);
        this.connectionFuture.completeExceptionally(cause);
        
        logger.warn("连接建立失败: {}, 清理缓存数据", connectionId, cause);
        
        // 清理缓存的数据
        clearPendingData();
    }
    
    /**
     * 发送数据
     */
    public CompletableFuture<Void> sendData(ByteBuf data) {
        if (!active) {
            ReferenceCountUtil.release(data);
            return CompletableFuture.failedFuture(new IllegalStateException("连接已关闭"));
        }
        
        // 如果连接已建立，直接发送
        if (connected.get() && backendChannel != null && backendChannel.isActive()) {
            return sendDataDirectly(data);
        }
        
        // 如果连接正在建立中，缓存数据
        if (connecting.get() || !connected.get()) {
            return cacheData(data);
        }
        
        // 连接状态异常
        ReferenceCountUtil.release(data);
        return CompletableFuture.failedFuture(new IllegalStateException("连接状态异常"));
    }
    
    /**
     * 直接发送数据
     */
    private CompletableFuture<Void> sendDataDirectly(ByteBuf data) {
        CompletableFuture<Void> future = new CompletableFuture<>();
        
        ChannelFuture channelFuture = backendChannel.writeAndFlush(data);
        channelFuture.addListener(f -> {
            if (f.isSuccess()) {
                bytesTransferred.addAndGet(data.readableBytes());
                markActive();
                future.complete(null);
            } else {
                logger.warn("发送数据失败: {}", connectionId, f.cause());
                future.completeExceptionally(f.cause());
            }
        });
        
        return future;
    }
    
    /**
     * 缓存数据到队列
     */
    private CompletableFuture<Void> cacheData(ByteBuf data) {
        // 检查队列大小限制
        if (pendingDataQueue.size() >= MAX_QUEUE_SIZE) {
            ReferenceCountUtil.release(data);
            return CompletableFuture.failedFuture(new IllegalStateException("缓存队列已满"));
        }
        
        // 增加引用计数，防止数据被释放
        data.retain();
        pendingDataQueue.offer(data);
        queuedBytes.addAndGet(data.readableBytes());
        
        logger.debug("数据已缓存: {}, 队列大小: {}, 缓存字节数: {}", 
                connectionId, pendingDataQueue.size(), queuedBytes.get());
        
        // 等待连接建立后发送
        return connectionFuture.thenCompose(channel -> {
            // 连接建立后，数据会在flushPendingData中被处理
            return CompletableFuture.completedFuture(null);
        });
    }
    
    /**
     * 处理缓存的数据
     */
    private void flushPendingData() {
        if (backendChannel == null || !backendChannel.isActive()) {
            logger.warn("后端连接不可用，无法处理缓存数据: {}", connectionId);
            clearPendingData();
            return;
        }
        
        int flushedCount = 0;
        long flushedBytes = 0;
        
        ByteBuf data;
        while ((data = pendingDataQueue.poll()) != null) {
            try {
                if (data.isReadable()) {
                    backendChannel.writeAndFlush(data);
                    flushedBytes += data.readableBytes();
                    flushedCount++;
                } else {
                    ReferenceCountUtil.release(data);
                }
            } catch (Exception e) {
                logger.warn("处理缓存数据时发生异常: {}", connectionId, e);
                ReferenceCountUtil.release(data);
            }
        }
        
        if (flushedCount > 0) {
            bytesTransferred.addAndGet(flushedBytes);
            queuedBytes.set(0);
            markActive();
            logger.debug("处理缓存数据完成: {}, 发送 {} 个数据包, {} 字节", 
                    connectionId, flushedCount, flushedBytes);
        }
    }
    
    /**
     * 清理缓存的数据
     */
    private void clearPendingData() {
        int clearedCount = 0;
        long clearedBytes = queuedBytes.get();
        
        ByteBuf data;
        while ((data = pendingDataQueue.poll()) != null) {
            ReferenceCountUtil.release(data);
            clearedCount++;
        }
        
        queuedBytes.set(0);
        
        if (clearedCount > 0) {
            logger.debug("清理缓存数据: {}, 清理 {} 个数据包, {} 字节", 
                    connectionId, clearedCount, clearedBytes);
        }
    }
    
    /**
     * 关闭连接
     */
    public CompletableFuture<Void> close() {
        if (!active) {
            return CompletableFuture.completedFuture(null);
        }
        
        active = false;
        
        // 清理缓存数据
        clearPendingData();
        
        // 关闭后端连接
        if (backendChannel != null && backendChannel.isActive()) {
            CompletableFuture<Void> future = new CompletableFuture<>();
            backendChannel.close().addListener(f -> {
                if (f.isSuccess()) {
                    future.complete(null);
                } else {
                    future.completeExceptionally(f.cause());
                }
            });
            return future;
        }
        
        return CompletableFuture.completedFuture(null);
    }
    
    // Getters
    public String getConnectionId() {
        return connectionId;
    }
    
    public Channel getBackendChannel() {
        return backendChannel;
    }
    
    public String getTargetHost() {
        return targetHost;
    }
    
    public int getTargetPort() {
        return targetPort;
    }
    
    public String getProtocol() {
        return protocol;
    }
    
    public long getCreateTime() {
        return createTime;
    }
    
    public long getBytesTransferred() {
        return bytesTransferred.get();
    }
    
    public Map<String, Object> getAttributes() {
        return attributes;
    }
    
    public boolean isActive() {
        return active && (connected.get() || connecting.get());
    }
    
    public boolean isConnected() {
        return connected.get() && backendChannel != null && backendChannel.isActive();
    }
    
    public boolean isConnecting() {
        return connecting.get();
    }
    
    public long getLastActiveTime() {
        return lastActiveTime;
    }
    
    public int getPendingDataCount() {
        return pendingDataQueue.size();
    }
    
    public long getQueuedBytes() {
        return queuedBytes.get();
    }

    /**
     * 获取连接建立的Future
     * 用于等待异步连接建立完成
     */
    public CompletableFuture<Channel> getConnectionFuture() {
        return connectionFuture;
    }
    
    // 状态管理方法
    public void markActive() {
        this.lastActiveTime = System.currentTimeMillis();
    }
    
    public void markInactive() {
        this.active = false;
    }
    
    // 便捷方法
    public String getTarget() {
        return targetHost + ":" + targetPort;
    }
    
    public long getConnectionAge() {
        return System.currentTimeMillis() - createTime;
    }
    
    public long getIdleTime() {
        return System.currentTimeMillis() - lastActiveTime;
    }
    
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key) {
        return (T) attributes.get(key);
    }
    
    public <T> T getAttribute(String key, T defaultValue) {
        T value = getAttribute(key);
        return value != null ? value : defaultValue;
    }
    
    public void setAttribute(String key, Object value) {
        attributes.put(key, value);
    }
    
    public boolean hasAttribute(String key) {
        return attributes.containsKey(key);
    }
    
    @Override
    public String toString() {
        return String.format("AsyncOutboundConnection{id='%s', target='%s:%d', protocol='%s', " +
                "connected=%s, connecting=%s, active=%s, pendingData=%d, queuedBytes=%d}",
                connectionId, targetHost, targetPort, protocol, 
                connected.get(), connecting.get(), active, 
                pendingDataQueue.size(), queuedBytes.get());
    }
    
    // Builder模式
    public static class Builder {
        private String connectionId;
        private Channel backendChannel;
        private String targetHost;
        private int targetPort;
        private String protocol;
        private long createTime;
        private Map<String, Object> attributes = new HashMap<>();
        
        public Builder connectionId(String connectionId) {
            this.connectionId = connectionId;
            return this;
        }
        
        public Builder backendChannel(Channel channel) {
            this.backendChannel = channel;
            return this;
        }
        
        public Builder target(String host, int port) {
            this.targetHost = host;
            this.targetPort = port;
            return this;
        }
        
        public Builder protocol(String protocol) {
            this.protocol = protocol;
            return this;
        }
        
        public Builder createTime(long createTime) {
            this.createTime = createTime;
            return this;
        }
        
        public Builder attribute(String key, Object value) {
            this.attributes.put(key, value);
            return this;
        }
        
        public Builder attributes(Map<String, Object> attributes) {
            this.attributes.putAll(attributes);
            return this;
        }
        
        public AsyncOutboundConnection build() {
            if (targetHost == null || targetHost.trim().isEmpty()) {
                throw new IllegalArgumentException("Target host cannot be null or empty");
            }
            if (targetPort <= 0 || targetPort > 65535) {
                throw new IllegalArgumentException("Target port must be between 1 and 65535");
            }
            if (protocol == null || protocol.trim().isEmpty()) {
                throw new IllegalArgumentException("Protocol cannot be null or empty");
            }
            
            return new AsyncOutboundConnection(this);
        }
    }
    
    // 静态工厂方法
    public static Builder builder() {
        return new Builder();
    }
    
    // 属性键常量
    public static final class Attributes {
        public static final String OUTBOUND_ID = "outbound.id";
        public static final String ROUTE_RULE_ID = "route.rule.id";
        public static final String CLIENT_CONNECTION_ID = "client.connection.id";
        public static final String SESSION_ID = "session.id";
        public static final String CONNECTION_POOL_KEY = "pool.key";
        public static final String RETRY_COUNT = "retry.count";
        public static final String CONNECT_TIME = "connect.time";
        public static final String FIRST_BYTE_TIME = "first.byte.time";
        public static final String ASYNC_CONNECTION= "async.connection";
    }
}
